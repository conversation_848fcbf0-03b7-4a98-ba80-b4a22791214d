import 'package:background_downloader/background_downloader.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/data_sources/training_consumption_datasource.dart';

import '../../../../core/refresh_token_interceptor/refresh_token_interceptor.mocks.dart';
import '../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../auth/auth_data_source/auth_data_source_test.mocks.dart';
import '../../training_consumption_seed.dart';
import 'training_consumption_datasource_test.mocks.dart';

@GenerateNiceMocks([MockSpec<FileDownloader>()])
void main() {
  late TrainingConsumptionDatasource dataSource;
  late MockDio mockDio;
  const trainingId = 'id';
  final mockAuthTokenProvider = MockAuthTokenProvider();
  late MockFileDownloader mockFileDownloader;

  setUpAll(() async {
    await testEnvSetup();
    mockDio = MockDio();
    mockFileDownloader = MockFileDownloader();
    dataSource = TrainingConsumptionDatasource(dio: mockDio);

    GetIt.instance.registerFactory<AuthTokenProvider>(() => mockAuthTokenProvider);

    GetIt.instance.registerSingleton<FileDownloader>(mockFileDownloader);
    when(mockDio.options).thenReturn(BaseOptions(baseUrl: 'https://example.com'));
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('get training consumption details', () {
    test('should perform a GET request', () async {
      when(mockDio.get(any)).thenAnswer(
        (_) async => Response(
          statusCode: 200,
          data: tTrainingConsumptionDetailsJson,
          requestOptions: RequestOptions(path: ApiConstants.applicantsTrainings + trainingId),
        ),
      );

      await dataSource.getTrainingConsumptionDetails(trainingId);

      verify(mockDio.get(ApiConstants.applicantsTrainings + trainingId)).called(1);
    });

    test('should throw a DioException when the response code is not 200 or 201', () {
      when(mockDio.get(any)).thenAnswer(
        (_) async => Response(
          statusCode: 404,
          requestOptions: RequestOptions(path: ApiConstants.applicantsTrainings + trainingId),
        ),
      );

      expect(
        () => dataSource.getTrainingConsumptionDetails(trainingId),
        throwsA(isA<DioException>()),
      );
    });
  });

  group('mark lesson as completed', () {
    test('should perform a PUT request', () async {
      when(mockDio.put(any, data: anyNamed('data'))).thenAnswer(
        (_) async => Response(
          statusCode: 200,
          requestOptions: RequestOptions(path: ApiConstants.applicantsTrainings + trainingId),
        ),
      );

      await dataSource.markLessonAsCompleted(trainingId: trainingId, lessonId: 'lessonId');

      verify(
        mockDio.put(ApiConstants.applicantsTrainings + trainingId, data: anyNamed('data')),
      ).called(1);
    });

    test('should throw a DioException when the response code is not 200 or 201', () {
      when(mockDio.put(any, data: anyNamed('data'))).thenAnswer(
        (_) async => Response(
          statusCode: 404,
          requestOptions: RequestOptions(path: ApiConstants.applicantsTrainings + trainingId),
        ),
      );

      expect(
        () => dataSource.markLessonAsCompleted(trainingId: trainingId, lessonId: 'lessonId'),
        throwsA(isA<DioException>()),
      );
    });
  });

  group('get video url', () {
    test('should perform a GET request', () async {
      when(mockDio.get(any)).thenAnswer(
        (_) async => Response(
          statusCode: 200,
          data: {Constants.preSignedUrl: 'url'},
          requestOptions: RequestOptions(path: '${ApiConstants.videoLessonPath}videoKey'),
        ),
      );

      await dataSource.getVideoUrl('videoKey');

      verify(mockDio.get('${ApiConstants.videoLessonPath}videoKey')).called(1);
    });

    test('should throw a DioException when the response code is not 200 or 201', () {
      when(mockDio.get(any)).thenAnswer(
        (_) async => Response(
          statusCode: 404,
          requestOptions: RequestOptions(path: '${ApiConstants.videoLessonPath}videoKey'),
        ),
      );

      expect(() => dataSource.getVideoUrl('videoKey'), throwsA(isA<DioException>()));
    });
  });

  group('download exception scenarios', () {
    test('should throw DioException when slide download fails', () {
      const resource = Resource(key: 'slideKey', originalFilename: 'slide.pdf', size: 100);
      const authToken = 'authToken';

      when(mockAuthTokenProvider.getToken()).thenAnswer((_) async => authToken);
      when(mockFileDownloader.download(any, onProgress: anyNamed('onProgress'))).thenAnswer(
        (_) => Future.value(TaskStatusUpdate(DownloadTask(url: ''), TaskStatus.failed)),
      );

      expect(() => dataSource.downloadSlide(resource, (progress) {}), throwsA(isA<DioException>()));
    });

    test('should throw DioException when file download fails', () {
      const resource = Resource(key: 'fileKey', originalFilename: 'file.jpg', size: 100);
      const authToken = 'authToken';

      when(mockAuthTokenProvider.getToken()).thenAnswer((_) async => authToken);
      when(mockFileDownloader.download(any)).thenAnswer(
        (_) => Future.value(TaskStatusUpdate(DownloadTask(url: ''), TaskStatus.failed)),
      );

      expect(
        () => dataSource.downloadFile(resource.key ?? '', resource.originalFilename),
        throwsA(isA<DioException>()),
      );
    });
  });
}
